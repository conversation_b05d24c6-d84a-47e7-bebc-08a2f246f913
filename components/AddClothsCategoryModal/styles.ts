import { View, Text, TouchableOpacity } from 'react-native';
import styled , { DefaultTheme } from 'styled-components/native';
import Input from '../common/Input';

export const AddClothsModalContainer = styled(View)`
  height: 80%;
  background-color: white;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
`;

export const AddClothsModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HeaderText = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaani;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;


export const AddClothsModalTitle = styled(Text)`
  font-size: 24px;
  font-family: MuktaVaaniSemiBold;
  color: #1C1C1C;
  text-align: center;
`;

export const AddClothsModalInput = styled(Input)`
  width: 100%;
  height: 40px;
  border-radius: 10px;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;


export const ClothItemContainer = styled(View)<{ width: number }>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  height: ${({ width }: { width: number }) => (width / 2) - 10}px;
  width: ${({ width }: { width: number }) => (width / 2) - 10}px;
`;

export const AddClothItemContainer = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  height: ${({ width }: { width: number }) => (width / 2) - 10}px;
  width: ${({ width }: { width: number }) => (width / 2) - 10}px;
`;


export const ClothItemImage = styled.Image`
  width: 100%;
  height: 100%;
  border-radius: 10px;
`;

export const ClothItemName = styled(Text)`
  font-size: 17px;
  font-family: MuktaVaaniRegular;
  color: #000;
`;

export const ClothesContainer = styled(View)`
  flex: 1;
`;

export const CheckBox = styled(View)`
  width: 20px;
  height: 20px;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  border-color: #000;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background-color: #FFF;
`;

export const CategoryItem = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 10px;
  margin-right: 10px;
  paddingHorizontal: 16px;
  paddingVertical: 8px;
  background-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? theme.brand.green[500] : '#F5F5F5'};
`;

export const CategoryItemText = styled(Text)<{ isSelected: boolean }>`
  font-size: 16px;
  font-family: MuktaVaaniMedium;
  color: ${({ isSelected }: { isSelected: boolean }) => isSelected ? '#FFF' : '#000'};
`;

export const ClothNameContainer = styled(View)`
  flex-direction: row;
  padding: 5px;
  border-radius: 5px;
`;

export const ItemCategoryText = styled(Text)`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: 12px;
  color: #767676;
  line-height: 16px;
`;

